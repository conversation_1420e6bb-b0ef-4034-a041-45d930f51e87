<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{2A32B933-7588-4891-BAD3-60858EA691DC}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>ProManage</RootNamespace>
    <AssemblyName>ProManage</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <StartupObject>ProManage.Program</StartupObject>
    <UseWindowsForms>true</UseWindowsForms>
    <MSBuildArchitecture>x86</MSBuildArchitecture>
    <GenerateResourceUsePreserializedResources>true</GenerateResourceUsePreserializedResources>
    <RuntimeIdentifiers>win-x64;win-x86</RuntimeIdentifiers>
    <DisableLicenseCompilation>true</DisableLicenseCompilation>
    <EnableLicenseCompilation>false</EnableLicenseCompilation>
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>
  <!-- Custom target to copy the System.Resources.Extensions.dll after build -->
  <Target Name="CopySystemResourcesExtensions" AfterTargets="Build">
    <Copy SourceFiles="$(SolutionDir)packages\System.Resources.Extensions.8.0.0\lib\net462\System.Resources.Extensions.dll" DestinationFolder="$(TargetDir)" SkipUnchangedFiles="true" ContinueOnError="true" />
    <Message Text="Copying System.Resources.Extensions.dll to output directory" Importance="high" />
  </Target>
  <PropertyGroup>
    <NoWarn>$(NoWarn);NU1101;CS1030;CS0618;MSB3245;MSB3246</NoWarn>
    <!-- Define compilation constants -->
    <DefineConstants>TRACE;DEBUG</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>app.manifest</ApplicationManifest>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.Images.v24.1, Version=24.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Pdf.v24.1.Core, Version=24.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Pdf.v24.1.Drawing, Version=24.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.RichEdit.v24.1.Export, Version=24.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Sparkline.v24.1.Core, Version=24.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Utils.v24.1.UI, Version=24.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Xpo.v24.1, Version=24.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraGauges.v24.1.Core, Version=24.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="MediaFoundation, Version=3.1.0.32450, Culture=neutral, PublicKeyToken=36fa660e1d6ebf8d, processorArchitecture=MSIL">
      <HintPath>lib\RCWF\2024.4.1113.48\MediaFoundation.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Bcl.AsyncInterfaces.9.0.5\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Toolkit.Uwp.Notifications, Version=7.0.0.0, Culture=neutral, PublicKeyToken=4aff67a105548ee2, processorArchitecture=MSIL">
      <HintPath>lib\RCWF\2024.4.1113.48\Microsoft.Toolkit.Uwp.Notifications.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Syncfusion.Compression.Base, Version=29.2462.4.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89" />
    <Reference Include="Syncfusion.Core.WinForms, Version=29.2462.4.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>packages\Syncfusion.Core.WinForms.29.2.4\lib\net462\Syncfusion.Core.WinForms.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.DataSource.WinForms, Version=29.2462.4.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>packages\Syncfusion.DataSource.WinForms.29.2.4\lib\net462\Syncfusion.DataSource.WinForms.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.Edit.Windows, Version=29.2462.4.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>packages\Syncfusion.Edit.Windows.29.2.4\lib\net462\Syncfusion.Edit.Windows.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.Grid.Base, Version=29.2462.4.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>packages\Syncfusion.Grid.Base.29.2.4\lib\net462\Syncfusion.Grid.Base.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.Grid.Windows, Version=29.2462.4.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>packages\Syncfusion.Grid.Windows.29.2.4\lib\net462\Syncfusion.Grid.Windows.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.GridCommon.WinForms, Version=29.2462.4.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>packages\Syncfusion.GridCommon.WinForms.29.2.4\lib\net462\Syncfusion.GridCommon.WinForms.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.Licensing, Version=29.2462.4.0, Culture=neutral, PublicKeyToken=632609b4d040f6b4, processorArchitecture=MSIL">
      <HintPath>packages\Syncfusion.Licensing.29.2.4\lib\net462\Syncfusion.Licensing.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.SfInput.WinForms, Version=29.2462.4.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>packages\Syncfusion.SfInput.WinForms.29.2.4\lib\net462\Syncfusion.SfInput.WinForms.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.SfListView.WinForms, Version=29.2462.4.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>packages\Syncfusion.SfListView.WinForms.29.2.4\lib\net462\Syncfusion.SfListView.WinForms.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.Shared.Base, Version=29.2462.4.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>packages\Syncfusion.Shared.Base.29.2.4\lib\net462\Syncfusion.Shared.Base.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.Shared.Windows, Version=29.2462.4.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>packages\Syncfusion.Shared.Windows.29.2.4\lib\net462\Syncfusion.Shared.Windows.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.SpellChecker.Base, Version=29.2462.4.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>packages\Syncfusion.SpellChecker.Base.29.2.4\lib\net462\Syncfusion.SpellChecker.Base.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.Spreadsheet.Windows, Version=29.2462.4.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL" />
    <Reference Include="Syncfusion.Tools.Base, Version=29.2462.4.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>packages\Syncfusion.Tools.Base.29.2.4\lib\net462\Syncfusion.Tools.Base.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.Tools.Windows, Version=29.2462.4.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>packages\Syncfusion.Tools.Windows.29.2.4\lib\net462\Syncfusion.Tools.Windows.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.XlsIO.Base, Version=29.2462.4.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89" />
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Design" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Diagnostics.DiagnosticSource.8.0.0\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.IO.Pipelines.9.0.5\lib\net462\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Text.Encodings.Web.9.0.5\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Text.Json.9.0.5\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Channels, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Threading.Channels.8.0.0\lib\net462\System.Threading.Channels.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Resources.Extensions">
      <HintPath>packages\System.Resources.Extensions.8.0.0\lib\net462\System.Resources.Extensions.dll</HintPath>
      <Private>True</Private>
      <EmbedInteropTypes>False</EmbedInteropTypes>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <!-- Npgsql references -->
    <Reference Include="Npgsql">
      <HintPath>packages\Npgsql.8.0.7\lib\netstandard2.0\Npgsql.dll</HintPath>
      <Private>True</Private>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <!-- Microsoft.Web.WebView2 references -->
    <Reference Include="Microsoft.Web.WebView2.Core">
      <HintPath>packages\Microsoft.Web.WebView2.1.0.1938.49\lib\net45\Microsoft.Web.WebView2.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Web.WebView2.WinForms">
      <HintPath>packages\Microsoft.Web.WebView2.1.0.1938.49\lib\net45\Microsoft.Web.WebView2.WinForms.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <!-- Syncfusion references -->
    <!-- Npgsql dependencies -->
    <Reference Include="System.Buffers">
      <HintPath>packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe">
      <HintPath>packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <!-- Microsoft.Bcl.AsyncInterfaces assembly reference -->
    <!-- The original Microsoft.EE.AsyncInterface reference is replaced with binding redirects in App.config -->
    <!-- We rely on the Microsoft.Bcl.AsyncInterfaces package that's already referenced above -->
    <!-- DevExpress references using NuGet packages -->
    <Reference Include="DevExpress.Data.v24.1">
      <HintPath>packages\DevExpress.Data.24.1.7\lib\net452\DevExpress.Data.v24.1.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Data.Desktop.v24.1">
      <HintPath>packages\DevExpress.Data.Desktop.24.1.7\lib\net452\DevExpress.Data.Desktop.v24.1.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Drawing.v24.1">
      <HintPath>packages\DevExpress.Drawing.24.1.7\lib\net452\DevExpress.Drawing.v24.1.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Printing.v24.1.Core">
      <HintPath>packages\DevExpress.Printing.Core.24.1.7\lib\net452\DevExpress.Printing.v24.1.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Utils.v24.1">
      <HintPath>packages\DevExpress.Utils.24.1.7\lib\net452\DevExpress.Utils.v24.1.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraBars.v24.1">
      <HintPath>packages\DevExpress.Win.Navigation.24.1.7\lib\net452\DevExpress.XtraBars.v24.1.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraEditors.v24.1">
      <HintPath>packages\DevExpress.Win.Navigation.24.1.7\lib\net452\DevExpress.XtraEditors.v24.1.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraLayout.v24.1">
      <HintPath>packages\DevExpress.Win.Navigation.24.1.7\lib\net452\DevExpress.XtraLayout.v24.1.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraNavBar.v24.1">
      <HintPath>packages\DevExpress.Win.Navigation.24.1.7\lib\net452\DevExpress.XtraNavBar.v24.1.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraPrinting.v24.1">
      <HintPath>packages\DevExpress.Win.Printing.24.1.7\lib\net452\DevExpress.XtraPrinting.v24.1.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraTreeList.v24.1">
      <HintPath>packages\DevExpress.Win.TreeList.24.1.7\lib\net452\DevExpress.XtraTreeList.v24.1.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraVerticalGrid.v24.1">
      <HintPath>packages\DevExpress.Win.Grid.24.1.7\lib\net452\DevExpress.XtraVerticalGrid.v24.1.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <!-- XtraTabbedMdiManager is part of DevExpress.XtraBars.v24.1 assembly -->
    <Reference Include="DevExpress.Office.v24.1.Core">
      <HintPath>packages\DevExpress.Office.Core.24.1.7\lib\net452\DevExpress.Office.v24.1.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.RichEdit.v24.1.Core">
      <HintPath>packages\DevExpress.RichEdit.Core.24.1.7\lib\net452\DevExpress.RichEdit.v24.1.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraRichEdit.v24.1">
      <HintPath>packages\DevExpress.Win.Navigation.24.1.7\lib\net452\DevExpress.XtraRichEdit.v24.1.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <!-- DevExpress.XtraRichEdit.v24.1.Extensions - Commented out as it requires local DevExpress installation -->
    <!-- <Reference Include="DevExpress.XtraRichEdit.v24.1.Extensions">
      <HintPath>D:\DevExpress 24.1\Components\Bin\Framework\DevExpress.XtraRichEdit.v24.1.Extensions.dll</HintPath>
      <Private>False</Private>
      <SpecificVersion>False</SpecificVersion>
    </Reference> -->
    <!-- Add XtraGrid references -->
    <Reference Include="DevExpress.XtraGrid.v24.1">
      <HintPath>packages\DevExpress.Win.Grid.24.1.7\lib\net452\DevExpress.XtraGrid.v24.1.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <!-- XtraReports references - directly from installed DevExpress folder -->
    <Reference Include="DevExpress.XtraReports.v24.1">
      <HintPath>D:\DevExpress 24.1\Components\Bin\Framework\DevExpress.XtraReports.v24.1.dll</HintPath>
      <Private>False</Private>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.XtraReports.v24.1.Extensions">
      <HintPath>D:\DevExpress 24.1\Components\Bin\Framework\DevExpress.XtraReports.v24.1.Extensions.dll</HintPath>
      <Private>False</Private>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.Charts.v24.1.Core">
      <HintPath>D:\DevExpress 24.1\Components\Bin\Framework\DevExpress.Charts.v24.1.Core.dll</HintPath>
      <Private>False</Private>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.PivotGrid.v24.1.Core">
      <HintPath>D:\DevExpress 24.1\Components\Bin\Framework\DevExpress.PivotGrid.v24.1.Core.dll</HintPath>
      <Private>False</Private>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <!-- Additional references for XtraReports Designer -->
    <Reference Include="DevExpress.XtraReports.v24.1.Design">
      <HintPath>D:\DevExpress 24.1\Components\Bin\Framework\DevExpress.XtraReports.v24.1.Design.dll</HintPath>
      <Private>False</Private>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.XtraBars.v24.1">
      <HintPath>packages\DevExpress.Win.Navigation.24.1.7\lib\net452\DevExpress.XtraBars.v24.1.dll</HintPath>
      <Private>True</Private>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <!-- XtraCharts references -->
    <Reference Include="DevExpress.XtraCharts.v24.1">
      <HintPath>D:\DevExpress 24.1\Components\Bin\Framework\DevExpress.XtraCharts.v24.1.dll</HintPath>
      <Private>False</Private>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.XtraCharts.v24.1.Extensions">
      <HintPath>D:\DevExpress 24.1\Components\Bin\Framework\DevExpress.XtraCharts.v24.1.Extensions.dll</HintPath>
      <Private>False</Private>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.XtraCharts.v24.1.UI">
      <HintPath>D:\DevExpress 24.1\Components\Bin\Framework\DevExpress.XtraCharts.v24.1.UI.dll</HintPath>
      <Private>False</Private>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.XtraCharts.v24.1.Wizard">
      <HintPath>D:\DevExpress 24.1\Components\Bin\Framework\DevExpress.XtraCharts.v24.1.Wizard.dll</HintPath>
      <Private>False</Private>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.CodeParser.v24.1">
      <HintPath>packages\DevExpress.XtraReports.24.1.7\lib\net452\DevExpress.CodeParser.v24.1.dll</HintPath>
      <Private>True</Private>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.DataAccess.v24.1">
      <HintPath>packages\DevExpress.XtraReports.24.1.7\lib\net452\DevExpress.DataAccess.v24.1.dll</HintPath>
      <Private>True</Private>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <!-- MSTest Framework references -->
    <Reference Include="Microsoft.VisualStudio.TestPlatform.TestFramework">
      <HintPath>packages\MSTest.TestFramework.3.1.1\lib\net462\Microsoft.VisualStudio.TestPlatform.TestFramework.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions">
      <HintPath>packages\MSTest.TestFramework.3.1.1\lib\net462\Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll</HintPath>
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Forms\ChildForms\AddRole.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ChildForms\AddRole.Designer.cs">
      <DependentUpon>AddRole.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\CommonForms\LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CommonForms\LoginForm.Designer.cs">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\CommonForms\MainFrame.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CommonForms\MainFrame.Designer.cs">
      <DependentUpon>MainFrame.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ReusableForms\MenuRibbon.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\ReusableForms\MenuRibbon.Designer.cs">
      <DependentUpon>MenuRibbon.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ReusableForms\PrintPreviewForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ReusableForms\PrintPreviewForm.Designer.cs">
      <DependentUpon>PrintPreviewForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\MainForms\UserManagementListForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainForms\UserManagementListForm.Designer.cs">
      <DependentUpon>UserManagementListForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\MainForms\RoleMasterForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainForms\RoleMasterForm.Designer.cs">
      <DependentUpon>RoleMasterForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\MainForms\PermissionManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainForms\PermissionManagementForm.Designer.cs">
      <DependentUpon>PermissionManagementForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\MainForms\UserMasterForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainForms\UserMasterForm.Designer.cs">
      <DependentUpon>UserMasterForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ReusableForms\ParamEntryForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ReusableForms\ParamEntryForm.Designer.cs">
      <DependentUpon>ParamEntryForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\MainForms\DatabaseForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainForms\DatabaseForm.Designer.cs">
      <DependentUpon>DatabaseForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ChildForms\EstimateForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ChildForms\EstimateForm.Designer.cs">
      <DependentUpon>EstimateForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\MainForms\ParametersForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainForms\ParametersForm.Designer.cs">
      <DependentUpon>ParametersForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\MainForms\SQLQueryForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainForms\SQLQueryForm.Designer.cs">
      <DependentUpon>SQLQueryForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\Models\EstimateForm\EstimateForm-HeaderModel.cs" />
    <Compile Include="Modules\Models\EstimateForm\EstimateForm-DetailModel.cs" />
    <Compile Include="Modules\Models\LoginForm\LoginForm-UserModel.cs" />
    <Compile Include="Modules\Models\ParametersForm\ParametersForm-Model.cs" />
    <Compile Include="Modules\Models\ParametersForm\ParameterType.cs" />
    <Compile Include="Modules\Models\UserMasterForm\UserMasterForm-Model.cs" />
    <Compile Include="Modules\Models\PermissionManagementForm\PermissionModels.cs" />
    <Compile Include="Modules\Models\PermissionManagementForm\RoleModels.cs" />
    <Compile Include="Modules\Models\PermissionManagementForm\UserPermissionModels.cs" />
    <Compile Include="Modules\Data\EstimateForm\EstimateForm-Repository.cs" />
    <Compile Include="Modules\Data\SQLQueries.cs" />
    <Compile Include="Modules\Data\EstimateForm\EstimateForm-QueryService.cs" />
    <Compile Include="Modules\Data\LoginForm\LoginForm-UserManager.cs" />
    <Compile Include="Modules\Data\ParametersForm\ParametersForm-Repository.cs" />
    <Compile Include="Modules\Data\UserMasterForm\UserMasterForm-Repository.cs" />
    <Compile Include="Modules\Connections\DatabaseConnectionManager.cs" />
    <Compile Include="Modules\Connections\DatabaseTransactionService.cs" />
    <Compile Include="Modules\Connections\DatabaseQueryExecutor.cs" />
    <Compile Include="Modules\Connections\PermissionDatabaseService.cs" />
    <Compile Include="Modules\Connections\RBACDatabaseSetup.cs" />
    <Compile Include="Modules\Helpers\SQLQueryLoader.cs" />
    <Compile Include="Modules\Helpers\ConfigurationHelper.cs" />
    <Compile Include="Modules\Helpers\EstimateForm\EstimateForm-EventHandlers.cs" />
    <Compile Include="Modules\Helpers\EstimateForm\EstimateForm-Helper.cs" />
    <Compile Include="Modules\Helpers\EstimateForm\EstimateForm-DataMapper.cs" />
    <Compile Include="Modules\Helpers\EstimateForm\EstimateForm-GridManager.cs" />
    <Compile Include="Modules\Helpers\EstimateForm\EstimateForm-Navigation.cs" />
    <Compile Include="Modules\Helpers\EstimateForm\EstimateForm-Validation.cs" />
    <Compile Include="Modules\Helpers\EstimateForm\EstimateForm-RepositoryHelper.cs" />
    <Compile Include="Modules\Helpers\EstimateForm\EstimateForm-GridEnhancements.cs" />
    <Compile Include="Modules\Helpers\EstimateForm\EstimateForm-StatusToggle.cs" />
    <Compile Include="Modules\Helpers\ParametersForm\ParametersForm-Helper.cs" />
    <Compile Include="Modules\Helpers\UserMasterForm\UserMasterForm-Navigation.cs" />
    <Compile Include="Modules\Helpers\PermissionManagementForm\PermissionGridHelper.cs" />
    <Compile Include="Modules\Services\ParameterCacheService.cs" />
    <Compile Include="Modules\Services\ParameterCacheModel.cs" />
    <Compile Include="Modules\Services\PasswordSecurityService.cs" />
    <Compile Include="Modules\Services\UnifiedParameterManager.cs" />
    <Compile Include="Modules\Services\PermissionService.cs" />
    <Compile Include="Modules\Services\PermissionCache.cs" />
    <Compile Include="Modules\Services\PermissionPerformanceMonitor.cs" />
    <Compile Include="Modules\Services\PermissionPreloader.cs" />
    <Compile Include="Modules\Services\GlobalPermissionService.cs" />
    <Compile Include="Modules\Services\GlobalPermissionEventArgs.cs" />
    <Compile Include="Modules\Services\FormsConfigurationService.cs" />
    <Compile Include="Modules\Services\FormDiscoveryService.cs" />
    <Compile Include="Modules\Services\FormSyncResult.cs" />
    <Compile Include="Modules\UI\ProgressIndicatorService.cs" />
    <Compile Include="Modules\Licensing\LicenseManager.cs" />
    <Compile Include="Modules\Licensing\LicenseUsageMode.cs" />
    <Compile Include="Modules\Components\PermissionDisplayGrid.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\Components\PermissionDisplayGrid.Designer.cs">
      <DependentUpon>PermissionDisplayGrid.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\Components\PermissionStatusIndicator.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\Components\PermissionSummaryPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Modules\Helpers\FormPermissionHelper.cs" />
    <Compile Include="Modules\Base\BasePermissionForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Modules\Base\BasePermissionForm.Designer.cs">
      <DependentUpon>BasePermissionForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\Application.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Modules\Reports\Estimate\EstimateForm-PrintLayout.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Modules\Reports\Estimate\EstimateForm-PrintLayout.Designer.cs">
      <DependentUpon>EstimateForm-PrintLayout.cs</DependentUpon>
    </Compile>
    <Compile Include="Modules\Reports\Estimate\EstimateReportService.cs" />
    <Compile Include="Tests\UnifiedParameterManagerTests.cs" />
    <Compile Include="Tests\EstimateFormSaveTest.cs" />
    <Compile Include="Tests\ParameterServiceTests.cs" />
    <Compile Include="Tests\PermissionSystemIntegrationTests.cs" />
    <Compile Include="Tests\PermissionManagementFormTests.cs" />
    <Compile Include="Tests\DatabaseSchemaFixTest.cs" />
    <Compile Include="Tests\TestRunner.cs" />
    <Compile Include="PermissionSystemFixer.cs" />
    <Compile Include="Modules\Testing\PermissionTestHelper.cs" />
    <Compile Include="Modules\Connections\DatabaseSchemaInitializer.cs" />
    <EmbeddedResource Include="Forms\ChildForms\AddRole.resx">
      <DependentUpon>AddRole.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\MainForms\DatabaseForm.resx">
      <DependentUpon>DatabaseForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ChildForms\EstimateForm.resx">
      <DependentUpon>EstimateForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\CommonForms\MainFrame.resx">
      <DependentUpon>MainFrame.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\MainForms\ParametersForm.resx">
      <DependentUpon>ParametersForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\MainForms\RoleMasterForm.resx">
      <DependentUpon>RoleMasterForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\MainForms\PermissionManagementForm.resx">
      <DependentUpon>PermissionManagementForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\MainForms\SQLQueryForm.resx">
      <DependentUpon>SQLQueryForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ReusableForms\MenuRibbon.resx">
      <DependentUpon>MenuRibbon.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ReusableForms\PrintPreviewForm.resx">
      <DependentUpon>PrintPreviewForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ReusableForms\ParamEntryForm.resx">
      <DependentUpon>ParamEntryForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\MainForms\UserMasterForm.resx">
      <DependentUpon>UserMasterForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\MainForms\UserManagementListForm.resx">
      <DependentUpon>UserManagementListForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <!-- License compilation disabled for testing -->
    <!-- <EmbeddedResource Include="Properties\licenses.licx" /> -->
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="Modules\Reports\Estimate\EstimateForm-PrintLayout.resx">
      <DependentUpon>EstimateForm-PrintLayout.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="app.manifest" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="Development.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Modules\Procedures\**\*.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\admin.svg" />
    <None Include="Resources\attachment.svg" />
    <None Include="Resources\home.svg" />
    <None Include="Resources\key.svg" />
    <None Include="Resources\settings.svg" />
    <None Include="Resources\toggle.svg" />
    <None Include="Resources\tools.svg" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="ProManage.Services\ProManage.Services.csproj">
      <Project>{B8D5F5A3-0E7A-4B1D-A7D7-5F7EC7825F0D}</Project>
      <Name>ProManage.Services</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\find.svg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\export.svg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\save.svg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Save1.svg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\quickprint.svg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\new.svg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\actions_edit.svg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\del.svg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\save2.svg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\parameters.svg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\preview.svg" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <ProjectExtensions>
    <VisualStudio>
      <UserProperties ShouldAddDPIScalingManifest="True" />
    </VisualStudio>
  </ProjectExtensions>
</Project>